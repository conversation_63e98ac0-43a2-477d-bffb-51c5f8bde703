#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';

// Import the existing MCP server from mcp-chrome-bridge
const { getMcpServer } = require('mcp-chrome-bridge/dist/mcp/mcp-server.js');

/**
 * Chrome MCP Server with stdio transport for Augment compatibility
 * This creates a stdio wrapper around the existing HTTP-based MCP server
 */
class ChromeMcpStdioServer {
  private server: Server;

  constructor() {
    // Get the existing MCP server instance
    this.server = getMcpServer();
  }

  async start(): Promise<void> {
    try {
      // Create stdio transport
      const transport = new StdioServerTransport();

      // Connect server to transport
      await this.server.connect(transport);

      console.error('Chrome MCP Server started with stdio transport');
    } catch (error) {
      console.error('Failed to start Chrome MCP Server:', error);
      process.exit(1);
    }
  }

  async stop(): Promise<void> {
    try {
      await this.server.close();
    } catch (error) {
      console.error('Error stopping server:', error);
    }
  }
}

// Handle process signals
const server = new ChromeMcpStdioServer();

process.on('SIGINT', async () => {
  console.error('Received SIGINT, shutting down gracefully...');
  await server.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.error('Received SIGTERM, shutting down gracefully...');
  await server.stop();
  process.exit(0);
});

// Start the server
server.start().catch((error) => {
  console.error('Failed to start server:', error);
  process.exit(1);
});
