#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';

// Import the existing MCP server from mcp-chrome-bridge
const { getMcpServer } = require('mcp-chrome-bridge/dist/mcp/mcp-server.js');

/**
 * Chrome MCP Server with stdio transport for Augment compatibility
 */
class ChromeMcpStdioServer {
  private server: Server;
  private extensionBridge: ChromeExtensionBridge;

  constructor() {
    this.server = new Server(
      {
        name: 'chrome-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.extensionBridge = new ChromeExtensionBridge();
    this.setupHandlers();
  }

  private setupHandlers(): void {
    // List tools handler
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return { tools: TOOL_SCHEMAS };
    });

    // Call tool handler
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      
      try {
        // Ensure extension bridge is connected
        await this.extensionBridge.ensureConnection();
        
        // Call the tool through the extension bridge
        const result = await this.extensionBridge.callTool(name, args || {});
        
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify(result, null, 2),
            },
          ],
          isError: false,
        };
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        
        return {
          content: [
            {
              type: 'text',
              text: `Error calling tool "${name}": ${errorMessage}`,
            },
          ],
          isError: true,
        };
      }
    });
  }

  async start(): Promise<void> {
    try {
      // Initialize the extension bridge
      await this.extensionBridge.initialize();
      
      // Create stdio transport
      const transport = new StdioServerTransport();
      
      // Connect server to transport
      await this.server.connect(transport);
      
      console.error('Chrome MCP Server started with stdio transport');
    } catch (error) {
      console.error('Failed to start Chrome MCP Server:', error);
      process.exit(1);
    }
  }

  async stop(): Promise<void> {
    try {
      await this.extensionBridge.disconnect();
      await this.server.close();
    } catch (error) {
      console.error('Error stopping server:', error);
    }
  }
}

// Handle process signals
const server = new ChromeMcpStdioServer();

process.on('SIGINT', async () => {
  console.error('Received SIGINT, shutting down gracefully...');
  await server.stop();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.error('Received SIGTERM, shutting down gracefully...');
  await server.stop();
  process.exit(0);
});

// Start the server
server.start().catch((error) => {
  console.error('Failed to start server:', error);
  process.exit(1);
});
